import { Checkbox } from '@chakra-ui/checkbox';
import { Box, Button, Dialog, Icon, Text, VStack } from '@chakra-ui/react';
import { type FC, useCallback, useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiInfo } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ButtonWithLoader, ModalCloseButton } from 'shared/components';
import { useShowError } from 'shared/hooks/alerts';
import { useGenerateSecretKey } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';

type Props = {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
};

export const checkBoxes = Object.freeze([
  { name: 'checkbox-1' },
  { name: 'checkbox-2' },
  { name: 'checkbox-3' },
]);

export const GenerateAPIkeyModal: FC<Props> = ({ open, ...rest }) => {
  return (
    <Dialog.Root
      open={open}
      onOpenChange={(details: any) => !details.open && rest.onClose()}
    >
      <Dialog.Backdrop display={['none', 'flex']} />
      <GenerateAPIkeyModalContent {...rest} />
    </Dialog.Root>
  );
};

export const GenerateAPIkeyModalContent: FC<Omit<Props, 'isOpen'>> = ({
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation('dev-tools');
  const [checkedItems, setCheckedState] = useState<Array<boolean>>(
    new Array(checkBoxes.length).fill(false),
  );
  const showError = useShowError();
  const { isLoading, generateSecretKey } = useGenerateSecretKey();
  const { merchantId } = useContext(GlobalStateContext);
  const handleOnChange = useCallback(
    (position: number) => {
      const newCheckedItems = checkedItems.map((item, idx) =>
        idx === position ? !item : item,
      );

      setCheckedState(newCheckedItems);
    },
    [checkedItems],
  );

  const onGenerateSecretKey = useCallback(async () => {
    if (!merchantId) {
      throw new Error('Merchant ID is missing');
    }

    const result = await generateSecretKey({ merchantId });

    if (result !== null) {
      onSuccess();
    }
  }, [generateSecretKey, onSuccess, merchantId]);

  const onHandleClick = useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    checkedItems.every(Boolean)
      ? onGenerateSecretKey()
      : showError(t('api-details.modal-error'));
  }, [checkedItems, t, showError, onGenerateSecretKey]);

  return (
    <Dialog.Content data-cy="generate-api-key-modal-content" maxW="29rem">
      <Dialog.Header>{t('api-details.modal.title')}</Dialog.Header>
      <ModalCloseButton />
      <Dialog.Body>
        <Text mb={5} textStyle="body2">
          {t('api-details.modal.description')}
        </Text>
        <VStack alignItems="stretch" mb={5} gap={3}>
          {checkBoxes.map(({ name }, idx) => (
            <Checkbox
              alignItems="flex-start"
              data-cy={`generate-api-key-modal-${name}`}
              disabled={isLoading}
              isChecked={checkedItems[idx]}
              key={name}
              onChange={() => {
                handleOnChange(idx);
              }}
            >
              {t(`api-details.modal-${name}`)}
            </Checkbox>
          ))}
        </VStack>
        <Box
          alignItems="center"
          backgroundColor="red.100"
          borderRadius="4px"
          color="red.900"
          display="flex"
          px={3}
          py={2}
        >
          <Icon as={FiInfo} boxSize={5} color="red.700" mr={2} />
          <Text textStyle="body2">{t('api-details.modal-warning')}</Text>
        </Box>
      </Dialog.Body>
      <Dialog.Footer>
        <Button
          colorScheme={ColorSchemes.SECONDARY}
          display={['none', 'inline-flex']}
          mr={[0, 3]}
          onClick={onClose}
        >
          {t('common:forms.cancel')}
        </Button>
        <ButtonWithLoader
          data-cy="generate-api-key-modal-submit"
          disabled={isLoading}
          loading={isLoading}
          onClick={onHandleClick}
        >
          {t('api-details.modal-submit')}
        </ButtonWithLoader>
      </Dialog.Footer>
    </Dialog.Content>
  );
};
