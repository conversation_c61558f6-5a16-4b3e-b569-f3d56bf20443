import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  Image,
  Link,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import * as Sentry from '@sentry/react';
import { useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { BiRightArrowAlt } from 'react-icons/bi';
import { useForwardLoanMutation } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { Header } from 'shared/components';
import {
  LocizeBusinessLoanKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useShowError } from 'shared/hooks/alerts';
import { usePageTitle } from 'shared/hooks/app';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { GlobalStateContext } from 'shared/hooks/state';

const TERMS_OF_SERVICE_LINK = 'https://www.hoovi.ee/tingimused';

const APPLY_CONS: Array<{
  title: string;
  description: string;
}> = [
  {
    title: LocizeBusinessLoanKeys.CARD_TITLE_1,
    description: LocizeBusinessLoanKeys.CARD_DESCRIPTION_1,
  },
  {
    title: LocizeBusinessLoanKeys.CARD_TITLE_2,
    description: LocizeBusinessLoanKeys.CARD_DESCRIPTION_2,
  },
  {
    title: LocizeBusinessLoanKeys.CARD_TITLE_3,
    description: LocizeBusinessLoanKeys.CARD_DESCRIPTION_3,
  },
];

const BusinessLoanPage = () => {
  const showError = useShowError();
  const { t } = useTranslation(LocizeNamespaces.BUSINESS_LOAN);
  const isMobileLayout = useIsMobileLayout();
  const { merchantId } = useContext(GlobalStateContext);

  const [forwardLoan, { loading }] = useForwardLoanMutation({
    variables: {
      merchantId,
    },
  });

  const handleApplyButtonClick = async () => {
    try {
      const { data, errors } = await forwardLoan();

      if (!data) {
        throw errors;
      }

      window.open(data.forward_loan, '_blank');
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          merchantId,
        },
      });

      if (error instanceof Error) {
        showError(error.message);
      }
    }
  };

  usePageTitle(t(LocizeBusinessLoanKeys.PAGE_TITLE));

  return (
    <>
      <Header title={t(LocizeBusinessLoanKeys.PAGE_TITLE)} />
      <Flex
        alignItems="center"
        flexDirection="column"
        height="100%"
        justifyContent="center"
        px="20px"
      >
        <VStack
          maxWidth={{ lg: '980px' }}
          overflow="auto"
          p={{ md: '80px 12px', lg: '80px 60px' }}
          gap={{ base: 6, lg: 4 }}
        >
          <Stack
            alignItems={{ lg: 'center' }}
            direction={{ base: 'column-reverse', md: 'column', lg: 'row' }}
            gap={{ base: 6, md: 9.25 }}
          >
            <VStack
              align={{ base: 'stretch', md: 'start' }}
              flex="1"
              gap={{ base: 6, md: 8 }}
            >
              <Text
                as="h1"
                fontSize={{ base: '3xl', md: '6xl' }}
                fontWeight={800}
                lineHeight={{ base: '36px', md: '60px' }}
              >
                {t(LocizeBusinessLoanKeys.PAGE_HEADING)}
              </Text>
              <Text color="gray.600" fontSize={{ base: 'lg', md: 'xl' }}>
                {t(LocizeBusinessLoanKeys.PAGE_DESCRIPTION)}
              </Text>
              <Button
                _hover={isMobileLayout ? {} : undefined}
                bg="#E63A27"
                borderRadius="6px"
                colorScheme={ColorSchemes.RED}
                loading={loading}
                onClick={handleApplyButtonClick}
              >
                {t(LocizeBusinessLoanKeys.BUTTON_TEXT)}
                <BiRightArrowAlt />
              </Button>
            </VStack>
            <Box
              borderRadius="8px"
              flex="1"
              overflow={{ base: 'hidden', md: 'initial' }}
            >
              <Image
                alt="Business loan"
                src={`/images/business-loan-${
                  isMobileLayout ? 'mobile' : 'desktop'
                }.webp`}
                w="100%"
              />
            </Box>
          </Stack>
          <VStack gap={9}>
            <Text color="gray.500" fontSize="xs">
              <Trans
                components={[
                  <Link
                    href={TERMS_OF_SERVICE_LINK}
                    isExternal
                    key="link"
                    textDecoration="underline"
                  />,
                ]}
                i18nKey={LocizeBusinessLoanKeys.SUB_DESCRIPTION}
                t={t}
              />
            </Text>
            <Grid
              gap={{ base: 2, md: 6 }}
              templateColumns={{ base: '1fr', lg: 'repeat(3,1fr)' }}
            >
              {APPLY_CONS.map(({ description, title }) => (
                <GridItem key={title}>
                  <VStack
                    align="start"
                    bg="gray.50"
                    borderRadius="8px"
                    h="100%"
                    p={4}
                    gap={2}
                  >
                    <Text fontSize="lg" fontWeight={700}>
                      {t(title)}
                    </Text>
                    <Text color="blackAlpha.700" fontSize="15px">
                      {t(description)}
                    </Text>
                  </VStack>
                </GridItem>
              ))}
            </Grid>
          </VStack>
        </VStack>
      </Flex>
      {/* </Layout> */}
    </>
  );
};

export default BusinessLoanPage;
